"use client";

import { useEffect, useState } from 'react';
import { createClient } from '@/utils/supabase/client';
import { Badge } from '@/components/ui/badge';
import { Wifi, WifiOff } from 'lucide-react';

export function ConnectionStatus() {
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  useEffect(() => {
    const supabase = createClient();
    
    const channel = supabase
      .channel('connection-status')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'leads' },
        () => {
          setLastUpdate(new Date());
          setIsConnected(true);
        }
      )
      .subscribe((status) => {
        setIsConnected(status === 'SUBSCRIBED');
      });

    // Test connection after 3 seconds
    setTimeout(() => {
      if (!isConnected) {
        setIsConnected(false);
      }
    }, 3000);

    return () => supabase.removeChannel(channel);
  }, []);

  return (
    <div className="flex items-center space-x-2">
      {isConnected ? (
        <Badge variant="secondary" className="bg-green-50 text-green-700 border-green-200">
          <Wifi className="h-3 w-3 mr-1" />
          Live
        </Badge>
      ) : (
        <Badge variant="secondary" className="bg-gray-50 text-gray-600 border-gray-200">
          <WifiOff className="h-3 w-3 mr-1" />
          Offline
        </Badge>
      )}
      
      {lastUpdate && (
        <span className="text-xs text-muted-foreground">
          Last update: {lastUpdate.toLocaleTimeString()}
        </span>
      )}
    </div>
  );
}
