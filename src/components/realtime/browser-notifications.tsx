"use client";

import { useEffect, useState } from 'react';
import { createClient } from '@/utils/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON>, BellOff, Check, X } from 'lucide-react';

export function BrowserNotifications() {
  const [permission, setPermission] = useState<NotificationPermission>('default');
  const [isSupported, setIsSupported] = useState(false);

  useEffect(() => {
    // Check if browser supports notifications
    setIsSupported('Notification' in window);
    
    if ('Notification' in window) {
      setPermission(Notification.permission);
    }
  }, []);

  const requestPermission = async () => {
    if (!isSupported) return;
    
    try {
      const result = await Notification.requestPermission();
      setPermission(result);
      
      if (result === 'granted') {
        // Show a test notification
        new Notification('🎉 Notifications Enabled!', {
          body: 'You will now receive browser notifications for new leads.',
          icon: '/favicon.ico',
          tag: 'test-notification'
        });
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
    }
  };

  const sendBrowserNotification = (title: string, options: NotificationOptions) => {
    if (permission === 'granted' && isSupported) {
      try {
        const notification = new Notification(title, {
          icon: '/favicon.ico',
          badge: '/favicon.ico',
          ...options
        });

        // Auto-close after 5 seconds
        setTimeout(() => {
          notification.close();
        }, 5000);

        // Handle click to focus window
        notification.onclick = () => {
          window.focus();
          notification.close();
        };
      } catch (error) {
        console.error('Error showing notification:', error);
      }
    }
  };

  // Set up real-time subscription for browser notifications
  useEffect(() => {
    if (permission !== 'granted') return;

    const supabase = createClient();
    
    const channel = supabase
      .channel('browser-notifications')
      .on(
        'postgres_changes',
        { 
          event: 'INSERT', 
          schema: 'public', 
          table: 'leads' 
        },
        (payload) => {
          const newLead = payload.new as any;
          
          const leadName = `${newLead.firstName || newLead.first_name || 'New'} ${newLead.lastName || newLead.last_name || 'Lead'}`.trim();
          const company = newLead.company || 'Unknown Company';
          const quality = newLead.leadQuality || newLead.lead_quality || 'unknown';
          
          let title = '🎉 New Lead Received!';
          let body = `${leadName} from ${company}`;
          
          // Customize notification based on lead quality
          if (quality === 'hot') {
            title = '🔥 Hot Lead Alert!';
            body = `${leadName} from ${company} - High Priority!`;
          } else if (quality === 'warm') {
            title = '⭐ Warm Lead Received';
            body = `${leadName} from ${company} - Good Potential`;
          }

          sendBrowserNotification(title, {
            body,
            tag: `lead-${newLead.id}`,
            requireInteraction: quality === 'hot', // Keep hot leads visible until clicked
            actions: [
              {
                action: 'view',
                title: 'View Dashboard'
              },
              {
                action: 'dismiss',
                title: 'Dismiss'
              }
            ]
          });
        }
      )
      .subscribe();

    return () => supabase.removeChannel(channel);
  }, [permission]);

  if (!isSupported) {
    return (
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-2 text-muted-foreground">
            <BellOff className="h-4 w-4" />
            <span className="text-sm">Browser notifications not supported</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center gap-2">
          <Bell className="h-4 w-4" />
          Browser Notifications
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Status:</span>
            <div className="flex items-center gap-2">
              {permission === 'granted' && (
                <>
                  <Check className="h-4 w-4 text-green-500" />
                  <span className="text-sm text-green-600">Enabled</span>
                </>
              )}
              {permission === 'denied' && (
                <>
                  <X className="h-4 w-4 text-red-500" />
                  <span className="text-sm text-red-600">Blocked</span>
                </>
              )}
              {permission === 'default' && (
                <span className="text-sm text-muted-foreground">Not set</span>
              )}
            </div>
          </div>

          {permission === 'default' && (
            <Button 
              onClick={requestPermission} 
              size="sm" 
              className="w-full"
            >
              <Bell className="h-4 w-4 mr-2" />
              Enable Notifications
            </Button>
          )}

          {permission === 'denied' && (
            <div className="text-xs text-muted-foreground">
              <p>Notifications are blocked. To enable:</p>
              <ol className="list-decimal list-inside mt-1 space-y-1">
                <li>Click the lock icon in your address bar</li>
                <li>Allow notifications for this site</li>
                <li>Refresh the page</li>
              </ol>
            </div>
          )}

          {permission === 'granted' && (
            <div className="text-xs text-green-600">
              ✅ You'll receive browser notifications for new leads, even when this tab is not active.
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
