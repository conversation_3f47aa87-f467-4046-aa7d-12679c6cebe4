"use client";

import { useEffect, useState } from "react";
import { createClient } from "@/utils/supabase/client";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Check<PERSON>ircle, XCircle, Loader2 } from "lucide-react";

export function RealtimeTest() {
  const [status, setStatus] = useState<"connecting" | "connected" | "error">(
    "connecting"
  );
  const [lastEvent, setLastEvent] = useState<string | null>(null);
  console.log(lastEvent);

  useEffect(() => {
    const supabase = createClient();

    const channel = supabase
      .channel("realtime-test")
      .on(
        "postgres_changes",
        { event: "*", schema: "public", table: "leads" },
        (payload) => {
          console.log("Realtime event received:", payload);
          setLastEvent(
            `${payload.eventType} at ${new Date().toLocaleTimeString()}`
          );
          setStatus("connected");
        }
      )
      .subscribe((status) => {
        console.log("Realtime subscription status:", status);
        if (status === "SUBSCRIBED") {
          setStatus("connected");
        } else if (status === "CHANNEL_ERROR" || status === "TIMED_OUT") {
          setStatus("error");
        }
      });

    // Test connection after 5 seconds
    setTimeout(() => {
      if (status === "connecting") {
        setStatus("error");
      }
    }, 5000);

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  const getStatusIcon = () => {
    switch (status) {
      case "connecting":
        return <Loader2 className="h-4 w-4 animate-spin" />;
      case "connected":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "error":
        return <XCircle className="h-4 w-4 text-red-500" />;
    }
  };

  const getStatusBadge = () => {
    switch (status) {
      case "connecting":
        return <Badge variant="secondary">Connecting...</Badge>;
      case "connected":
        return (
          <Badge variant="default" className="bg-green-500">
            Connected
          </Badge>
        );
      case "error":
        return <Badge variant="destructive">Error</Badge>;
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {getStatusIcon()}
          Realtime Status
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Connection:</span>
          {getStatusBadge()}
        </div>

        {lastEvent && (
          <div className="space-y-2">
            <span className="text-sm text-muted-foreground">Last Event:</span>
            <p className="text-sm font-mono bg-muted p-2 rounded">
              {lastEvent}
            </p>
          </div>
        )}

        {status === "error" && (
          <div className="text-sm text-red-600">
            <p>Realtime connection failed. Please check:</p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>Supabase Realtime is enabled</li>
              <li>RLS policies allow realtime access</li>
              <li>Network connectivity</li>
            </ul>
          </div>
        )}

        {status === "connected" && (
          <div className="text-sm text-green-600">
            <p>
              ✅ Realtime is working! Submit a lead form to see live updates.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
