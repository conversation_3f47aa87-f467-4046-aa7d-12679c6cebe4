"use client";

import { useEffect, useState } from "react";
import { createClient } from "@/utils/supabase/client";
import { toast } from "sonner";
import {
  User,
  Bell,
  TrendingUp,
  Mail,
  Building,
  Phone,
  Star,
  Target,
  Award,
} from "lucide-react";
import { mutate } from "swr";

// Sound notification function
const playNotificationSound = () => {
  try {
    // Create a simple notification sound using Web Audio API
    const audioContext = new (window.AudioContext ||
      (window as any).webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
    oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
    oscillator.frequency.setValueAtTime(800, audioContext.currentTime + 0.2);

    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(
      0.01,
      audioContext.currentTime + 0.3
    );

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.3);
  } catch (error) {
    console.log("Audio notification not available:", error);
  }
};

// Get lead quality icon and color
const getLeadQualityInfo = (quality: string) => {
  switch (quality) {
    case "hot":
      return {
        icon: <TrendingUp className="h-4 w-4" />,
        color: "text-red-500",
        emoji: "🔥",
      };
    case "warm":
      return {
        icon: <Target className="h-4 w-4" />,
        color: "text-orange-500",
        emoji: "⭐",
      };
    case "cold":
      return {
        icon: <User className="h-4 w-4" />,
        color: "text-blue-500",
        emoji: "❄️",
      };
    default:
      return {
        icon: <User className="h-4 w-4" />,
        color: "text-gray-500",
        emoji: "👤",
      };
  }
};

export function RealtimeNotifications() {
  const [notificationCount, setNotificationCount] = useState(0);
  const [settings, setSettings] = useState({
    toastNotifications: true,
    soundNotifications: true,
    browserNotifications: false,
    hotLeadAlerts: true,
    statusChangeAlerts: true,
    qualityUpgradeAlerts: true,
  });

  // Load notification settings
  useEffect(() => {
    const loadSettings = () => {
      const savedSettings = localStorage.getItem("notification-settings");
      if (savedSettings) {
        try {
          setSettings(JSON.parse(savedSettings));
        } catch (error) {
          console.error("Error loading notification settings:", error);
        }
      }
    };

    loadSettings();

    // Listen for settings changes
    const handleSettingsChange = (event: CustomEvent) => {
      setSettings(event.detail);
    };

    window.addEventListener(
      "notification-settings-changed",
      handleSettingsChange as EventListener
    );

    return () => {
      window.removeEventListener(
        "notification-settings-changed",
        handleSettingsChange as EventListener
      );
    };
  }, []);

  useEffect(() => {
    const supabase = createClient();

    const channel = supabase
      .channel("lead-notifications")
      .on(
        "postgres_changes",
        {
          event: "INSERT",
          schema: "public",
          table: "leads",
        },
        (payload) => {
          const newLead = payload.new as any;
          console.log("🎉 New lead received:", newLead);

          // Increment notification count
          setNotificationCount((prev) => prev + 1);

          // Play notification sound if enabled
          if (settings.soundNotifications) {
            playNotificationSound();
          }

          // Get lead info
          const leadName = `${
            newLead.firstName || newLead.first_name || "New"
          } ${newLead.lastName || newLead.last_name || "Lead"}`.trim();
          const company = newLead.company || "Unknown Company";
          const email = newLead.email || "No email";
          const phone = newLead.phone || null;
          const quality =
            newLead.leadQuality || newLead.lead_quality || "unknown";
          const source = newLead.leadSource || newLead.lead_source || "website";

          const qualityInfo = getLeadQualityInfo(quality);

          // Main notification (only if toast notifications are enabled)
          if (settings.toastNotifications) {
            toast.success(`${qualityInfo.emoji} New Lead Received!`, {
              description: (
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <User className="h-3 w-3" />
                    <span className="font-medium">{leadName}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Building className="h-3 w-3" />
                    <span className="text-sm">{company}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Mail className="h-3 w-3" />
                    <span className="text-xs text-muted-foreground">
                      {email}
                    </span>
                  </div>
                  {phone && (
                    <div className="flex items-center gap-2">
                      <Phone className="h-3 w-3" />
                      <span className="text-xs text-muted-foreground">
                        {phone}
                      </span>
                    </div>
                  )}
                  <div className="flex items-center gap-2">
                    {qualityInfo.icon}
                    <span
                      className={`text-xs font-medium ${qualityInfo.color}`}
                    >
                      {quality.toUpperCase()} LEAD
                    </span>
                  </div>
                </div>
              ),
              duration: 8000,
              action: {
                label: "View Details",
                onClick: () => {
                  // You can add navigation to lead details here
                  window.location.href = "/private";
                },
              },
            });
          }

          // Special notifications based on lead quality (only if hot lead alerts are enabled)
          if (
            quality === "hot" &&
            settings.hotLeadAlerts &&
            settings.toastNotifications
          ) {
            setTimeout(() => {
              toast.success("🚨 High Priority Lead!", {
                description: `${leadName} from ${company} is a hot lead with high conversion potential!`,
                icon: <Award className="h-4 w-4" />,
                duration: 10000,
                className: "border-red-500 bg-red-50",
                action: {
                  label: "Contact Now",
                  onClick: () => {
                    // You can add quick contact functionality here
                    console.log("Quick contact for hot lead:", newLead);
                  },
                },
              });
            }, 1000);
          }

          // Source-specific notifications (only if toast notifications are enabled)
          if (source.includes("form") && settings.toastNotifications) {
            toast.info("📝 Form Submission", {
              description: `Lead came from ${source.replace("_", " ")}`,
              duration: 3000,
            });
          }

          // Automatically revalidate SWR cache for lead data
          setTimeout(() => {
            mutate(
              (key) => typeof key === "string" && key.includes("/api/leads")
            );
            mutate("/api/dashboard/stats");
          }, 500);
        }
      )
      .on(
        "postgres_changes",
        {
          event: "UPDATE",
          schema: "public",
          table: "leads",
        },
        (payload) => {
          const updatedLead = payload.new as any;
          const oldLead = payload.old as any;

          const leadName = `${
            updatedLead.firstName || updatedLead.first_name || "Lead"
          } ${updatedLead.lastName || updatedLead.last_name || ""}`.trim();
          const company = updatedLead.company || "Unknown Company";

          // Show notification for important status changes
          const oldStatus = oldLead.lead_status || oldLead.leadStatus;
          const newStatus = updatedLead.lead_status || updatedLead.leadStatus;

          if (oldStatus !== newStatus && settings.statusChangeAlerts) {
            switch (newStatus) {
              case "converted":
                if (settings.soundNotifications) {
                  playNotificationSound();
                }
                if (settings.toastNotifications) {
                  toast.success("🎉 Lead Converted!", {
                    description: `${leadName} from ${company} has been successfully converted!`,
                    icon: <Award className="h-4 w-4" />,
                    duration: 8000,
                    className: "border-green-500 bg-green-50",
                    action: {
                      label: "View Details",
                      onClick: () => {
                        console.log("View converted lead:", updatedLead);
                      },
                    },
                  });
                }
                break;
              case "qualified":
                if (settings.toastNotifications) {
                  toast.success("✅ Lead Qualified!", {
                    description: `${leadName} has been qualified and is ready for follow-up`,
                    icon: <Target className="h-4 w-4" />,
                    duration: 5000,
                  });
                }
                break;
              case "contacted":
                if (settings.toastNotifications) {
                  toast.info("📞 Lead Contacted", {
                    description: `${leadName} has been contacted`,
                    duration: 3000,
                  });
                }
                break;
              case "lost":
                if (settings.toastNotifications) {
                  toast.error("❌ Lead Lost", {
                    description: `${leadName} has been marked as lost`,
                    duration: 4000,
                  });
                }
                break;
            }
          }

          // Show notification for quality changes
          const oldQuality = oldLead.lead_quality || oldLead.leadQuality;
          const newQuality =
            updatedLead.lead_quality || updatedLead.leadQuality;

          if (
            oldQuality !== newQuality &&
            newQuality === "hot" &&
            settings.qualityUpgradeAlerts &&
            settings.toastNotifications
          ) {
            toast.success("🔥 Lead Upgraded to Hot!", {
              description: `${leadName} is now a hot lead with high conversion potential`,
              icon: <TrendingUp className="h-4 w-4" />,
              duration: 6000,
              className: "border-red-500 bg-red-50",
            });
          }

          // Revalidate cache for updates
          setTimeout(() => {
            mutate(
              (key) => typeof key === "string" && key.includes("/api/leads")
            );
            mutate("/api/dashboard/stats");
          }, 500);
        }
      )
      .subscribe((status) => {
        console.log("Real-time subscription status:", status);
      });

    // Log subscription status
    console.log("🔔 Real-time notifications subscribed");

    return () => {
      console.log("🔕 Real-time notifications unsubscribed");
      supabase.removeChannel(channel);
    };
  }, []);

  // Optional: Return a notification counter badge (you can use this in your header)
  return notificationCount > 0 ? (
    <div className="fixed top-4 right-4 z-50 pointer-events-none">
      <div className="bg-green-500 text-white text-xs px-2 py-1 rounded-full shadow-lg animate-pulse">
        {notificationCount} new lead{notificationCount > 1 ? "s" : ""}
      </div>
    </div>
  ) : null;
}
