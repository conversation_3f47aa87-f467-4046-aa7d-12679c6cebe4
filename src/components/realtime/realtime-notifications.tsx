"use client";

import { useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';
import { toast } from 'sonner';
import { User, Bell, TrendingUp } from 'lucide-react';
import { mutate } from 'swr';

export function RealtimeNotifications() {
  useEffect(() => {
    const supabase = createClient();
    
    const channel = supabase
      .channel('lead-notifications')
      .on(
        'postgres_changes',
        { 
          event: 'INSERT', 
          schema: 'public', 
          table: 'leads' 
        },
        (payload) => {
          const newLead = payload.new as any;
          
          // Show notification with lead details
          toast.success('🎉 New Lead Received!', {
            description: `${newLead.firstName || newLead.first_name || 'New lead'} ${newLead.lastName || newLead.last_name || ''} from ${newLead.company || 'Unknown company'}`,
            icon: <User className="h-4 w-4" />,
            duration: 5000,
            action: {
              label: 'View Dashboard',
              onClick: () => {
                // Trigger SWR revalidation for all lead-related data
                mutate(key => typeof key === 'string' && key.includes('/api/leads'));
                mutate('/api/dashboard/stats');
              }
            }
          });

          // Show additional info based on lead quality
          if (newLead.leadQuality === 'hot' || newLead.lead_quality === 'hot') {
            toast.success('🔥 Hot Lead Alert!', {
              description: 'This lead has high conversion potential',
              icon: <TrendingUp className="h-4 w-4" />,
              duration: 7000,
            });
          }

          // Automatically revalidate SWR cache for lead data
          // Use a small delay to ensure the database transaction is complete
          setTimeout(() => {
            mutate(key => typeof key === 'string' && key.includes('/api/leads'));
            mutate('/api/dashboard/stats');
          }, 500);
        }
      )
      .on(
        'postgres_changes',
        { 
          event: 'UPDATE', 
          schema: 'public', 
          table: 'leads' 
        },
        (payload) => {
          const updatedLead = payload.new as any;
          const oldLead = payload.old as any;
          
          // Show notification for important status changes
          if (oldLead.lead_status !== updatedLead.lead_status || oldLead.leadStatus !== updatedLead.leadStatus) {
            const newStatus = updatedLead.lead_status || updatedLead.leadStatus;
            
            if (newStatus === 'converted') {
              toast.success('💰 Lead Converted!', {
                description: `${updatedLead.firstName || updatedLead.first_name} ${updatedLead.lastName || updatedLead.last_name} has been converted`,
                icon: <TrendingUp className="h-4 w-4" />,
                duration: 5000,
              });
            }
          }

          // Revalidate cache for updates
          setTimeout(() => {
            mutate(key => typeof key === 'string' && key.includes('/api/leads'));
            mutate('/api/dashboard/stats');
          }, 500);
        }
      )
      .subscribe();

    // Log subscription status
    console.log('Real-time notifications subscribed');

    return () => {
      console.log('Real-time notifications unsubscribed');
      supabase.removeChannel(channel);
    };
  }, []);

  return null; // This component only handles notifications
}
