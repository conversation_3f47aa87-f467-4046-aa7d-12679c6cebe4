"use client";

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Settings, Volume2, VolumeX, Bell, BellOff } from 'lucide-react';

interface NotificationSettings {
  toastNotifications: boolean;
  soundNotifications: boolean;
  browserNotifications: boolean;
  hotLeadAlerts: boolean;
  statusChangeAlerts: boolean;
  qualityUpgradeAlerts: boolean;
}

const defaultSettings: NotificationSettings = {
  toastNotifications: true,
  soundNotifications: true,
  browserNotifications: false,
  hotLeadAlerts: true,
  statusChangeAlerts: true,
  qualityUpgradeAlerts: true,
};

export function NotificationSettings() {
  const [settings, setSettings] = useState<NotificationSettings>(defaultSettings);
  const [isOpen, setIsOpen] = useState(false);

  // Load settings from localStorage on mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('notification-settings');
    if (savedSettings) {
      try {
        setSettings(JSON.parse(savedSettings));
      } catch (error) {
        console.error('Error loading notification settings:', error);
      }
    }
  }, []);

  // Save settings to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('notification-settings', JSON.stringify(settings));
    
    // Dispatch custom event so other components can listen for changes
    window.dispatchEvent(new CustomEvent('notification-settings-changed', {
      detail: settings
    }));
  }, [settings]);

  const updateSetting = (key: keyof NotificationSettings, value: boolean) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const resetToDefaults = () => {
    setSettings(defaultSettings);
  };

  const testNotification = () => {
    // Dispatch a test event
    window.dispatchEvent(new CustomEvent('test-notification', {
      detail: {
        type: 'new-lead',
        data: {
          firstName: 'Test',
          lastName: 'User',
          company: 'Test Company',
          leadQuality: 'hot'
        }
      }
    }));
  };

  if (!isOpen) {
    return (
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsOpen(true)}
        className="flex items-center gap-2"
      >
        <Settings className="h-4 w-4" />
        Notification Settings
      </Button>
    );
  }

  return (
    <Card className="w-80">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Notification Settings
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsOpen(false)}
            className="h-6 w-6 p-0"
          >
            ×
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* General Notifications */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium">General</h4>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {settings.toastNotifications ? (
                <Bell className="h-4 w-4 text-green-500" />
              ) : (
                <BellOff className="h-4 w-4 text-muted-foreground" />
              )}
              <Label htmlFor="toast" className="text-sm">Toast Notifications</Label>
            </div>
            <Switch
              id="toast"
              checked={settings.toastNotifications}
              onCheckedChange={(checked) => updateSetting('toastNotifications', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {settings.soundNotifications ? (
                <Volume2 className="h-4 w-4 text-green-500" />
              ) : (
                <VolumeX className="h-4 w-4 text-muted-foreground" />
              )}
              <Label htmlFor="sound" className="text-sm">Sound Alerts</Label>
            </div>
            <Switch
              id="sound"
              checked={settings.soundNotifications}
              onCheckedChange={(checked) => updateSetting('soundNotifications', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Bell className="h-4 w-4" />
              <Label htmlFor="browser" className="text-sm">Browser Notifications</Label>
            </div>
            <Switch
              id="browser"
              checked={settings.browserNotifications}
              onCheckedChange={(checked) => updateSetting('browserNotifications', checked)}
            />
          </div>
        </div>

        <Separator />

        {/* Specific Alert Types */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium">Alert Types</h4>
          
          <div className="flex items-center justify-between">
            <Label htmlFor="hot-leads" className="text-sm">🔥 Hot Lead Alerts</Label>
            <Switch
              id="hot-leads"
              checked={settings.hotLeadAlerts}
              onCheckedChange={(checked) => updateSetting('hotLeadAlerts', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="status-changes" className="text-sm">📊 Status Changes</Label>
            <Switch
              id="status-changes"
              checked={settings.statusChangeAlerts}
              onCheckedChange={(checked) => updateSetting('statusChangeAlerts', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="quality-upgrades" className="text-sm">⭐ Quality Upgrades</Label>
            <Switch
              id="quality-upgrades"
              checked={settings.qualityUpgradeAlerts}
              onCheckedChange={(checked) => updateSetting('qualityUpgradeAlerts', checked)}
            />
          </div>
        </div>

        <Separator />

        {/* Actions */}
        <div className="space-y-2">
          <Button
            onClick={testNotification}
            variant="outline"
            size="sm"
            className="w-full"
          >
            Test Notification
          </Button>
          
          <Button
            onClick={resetToDefaults}
            variant="ghost"
            size="sm"
            className="w-full text-muted-foreground"
          >
            Reset to Defaults
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
