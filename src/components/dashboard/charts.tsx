"use client";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
} from "@/components/ui/chart";
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
} from "recharts";
import type {
  LeadTrendData,
  SourceDistribution,
  ConversionFunnelData,
  ChartDataPoint,
} from "@/lib/database/analytics";

// Lead Trends Line Chart
interface LeadTrendsChartProps {
  data: LeadTrendData[];
}

export function LeadTrendsChart({ data }: LeadTrendsChartProps) {
  const chartConfig = {
    total: {
      label: "Total Leads",
      color: "var(--chart-1)",
    },
    hot: {
      label: "Hot Leads",
      color: "var(--chart-2)",
    },
    warm: {
      label: "Warm Leads",
      color: "var(--chart-3)",
    },
    cold: {
      label: "Cold Leads",
      color: "var(--chart-4)",
    },
    converted: {
      label: "Converted",
      color: "var(--chart-5)",
    },
  };

  return (
    <Card className="border-0 shadow-lg">
      <CardHeader className="pb-4">
        <CardTitle className="text-xl font-semibold">Lead Trends</CardTitle>
        <CardDescription className="text-sm text-muted-foreground">
          Daily lead generation and quality trends over the last 30 days
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <ChartContainer
          config={chartConfig}
          className="aspect-auto h-[300px] w-full"
        >
          <LineChart
            accessibilityLayer
            data={data}
            margin={{
              top: 20,
              right: 20,
              left: 20,
              bottom: 20,
            }}
          >
            <CartesianGrid
              strokeDasharray="3 3"
              vertical={false}
              className="stroke-muted/30"
            />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={(value) => {
                const date = new Date(value);
                return date.toLocaleDateString("en-US", {
                  month: "short",
                  day: "numeric",
                });
              }}
              className="text-xs fill-muted-foreground"
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => value.toLocaleString()}
              className="text-xs fill-muted-foreground"
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  labelFormatter={(value) => {
                    return new Date(value).toLocaleDateString("en-US", {
                      month: "long",
                      day: "numeric",
                      year: "numeric",
                    });
                  }}
                  formatter={(value, name) => [
                    value.toLocaleString(),
                    chartConfig[name as keyof typeof chartConfig]?.label ||
                      name,
                  ]}
                />
              }
              cursor={{ stroke: "var(--muted)", strokeWidth: 1 }}
            />
            <ChartLegend content={<ChartLegendContent />} />
            <Line
              type="monotone"
              dataKey="total"
              stroke="var(--color-total)"
              strokeWidth={3}
              dot={{ r: 4, strokeWidth: 2, fill: "var(--color-total)" }}
              activeDot={{ r: 6, strokeWidth: 2 }}
            />
            <Line
              type="monotone"
              dataKey="hot"
              stroke="var(--color-hot)"
              strokeWidth={2.5}
              dot={{ r: 3, strokeWidth: 2, fill: "var(--color-hot)" }}
              activeDot={{ r: 5, strokeWidth: 2 }}
            />
            <Line
              type="monotone"
              dataKey="warm"
              stroke="var(--color-warm)"
              strokeWidth={2.5}
              dot={{ r: 3, strokeWidth: 2, fill: "var(--color-warm)" }}
              activeDot={{ r: 5, strokeWidth: 2 }}
            />
            <Line
              type="monotone"
              dataKey="cold"
              stroke="var(--color-cold)"
              strokeWidth={2.5}
              dot={{ r: 3, strokeWidth: 2, fill: "var(--color-cold)" }}
              activeDot={{ r: 5, strokeWidth: 2 }}
            />
            <Line
              type="monotone"
              dataKey="converted"
              stroke="var(--color-converted)"
              strokeWidth={2.5}
              dot={{ r: 3, strokeWidth: 2, fill: "var(--color-converted)" }}
              activeDot={{ r: 5, strokeWidth: 2 }}
            />
          </LineChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}

// Source Distribution Pie Chart
interface SourceDistributionChartProps {
  data: SourceDistribution[];
}

export function SourceDistributionChart({
  data,
}: SourceDistributionChartProps) {
  const chartConfig = data.reduce((config, item, index) => {
    config[item.name.toLowerCase().replace(/\s+/g, "_")] = {
      label: item.name,
      color: `var(--chart-${(index % 5) + 1})`,
    };
    return config;
  }, {} as Record<string, { label: string; color: string }>);

  return (
    <Card className="border-0 shadow-lg">
      <CardHeader className="pb-4">
        <CardTitle className="text-xl font-semibold">Lead Sources</CardTitle>
        <CardDescription className="text-sm text-muted-foreground">
          Distribution of leads by acquisition source
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <ChartContainer
          config={chartConfig}
          className="aspect-square h-[300px] w-full"
        >
          <PieChart>
            <ChartTooltip
              content={
                <ChartTooltipContent
                  formatter={(value, name) => [
                    `${value.toLocaleString()} leads`,
                    chartConfig[name as keyof typeof chartConfig]?.label ||
                      name,
                  ]}
                  labelFormatter={(label, payload) => {
                    const item = data.find((d) => d.name === label);
                    return item ? `${item.percentage}% of total leads` : label;
                  }}
                />
              }
            />
            <Pie
              data={data}
              dataKey="value"
              nameKey="name"
              cx="50%"
              cy="50%"
              innerRadius={60}
              outerRadius={120}
              paddingAngle={2}
              stroke="var(--background)"
              strokeWidth={2}
            >
              {data.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={entry.color || `var(--chart-${(index % 5) + 1}))`}
                  className="hover:opacity-80 transition-opacity duration-200"
                />
              ))}
            </Pie>
            <ChartLegend
              content={<ChartLegendContent />}
              wrapperStyle={{
                paddingTop: "20px",
              }}
            />
          </PieChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}

// Conversion Funnel Chart
interface ConversionFunnelChartProps {
  data: ConversionFunnelData[];
}

export function ConversionFunnelChart({ data }: ConversionFunnelChartProps) {
  const chartConfig = data.reduce((config, item, index) => {
    config[item.stage.toLowerCase().replace(/\s+/g, "_")] = {
      label: item.stage,
      color: item.color,
    };
    return config;
  }, {} as Record<string, { label: string; color: string }>);

  return (
    <Card className="border-0 shadow-lg">
      <CardHeader className="pb-4">
        <CardTitle className="text-xl font-semibold">
          Conversion Funnel
        </CardTitle>
        <CardDescription className="text-sm text-muted-foreground">
          Lead progression through sales stages
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <ChartContainer
          config={chartConfig}
          className="aspect-auto h-[300px] w-full"
        >
          <BarChart
            accessibilityLayer
            data={data}
            layout="horizontal"
            margin={{
              top: 20,
              right: 20,
              left: 80,
              bottom: 20,
            }}
          >
            <CartesianGrid
              strokeDasharray="3 3"
              horizontal={false}
              className="stroke-muted/30"
            />
            <XAxis
              type="number"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => value.toLocaleString()}
              className="text-xs fill-muted-foreground"
            />
            <YAxis
              dataKey="stage"
              type="category"
              width={80}
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              className="text-xs fill-muted-foreground"
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  formatter={(value, name) => [
                    `${value.toLocaleString()} leads`,
                    chartConfig[name as keyof typeof chartConfig]?.label ||
                      name,
                  ]}
                  labelFormatter={(label) => {
                    const item = data.find((d) => d.stage === label);
                    return item ? `${item.stage} (${item.percentage}%)` : label;
                  }}
                />
              }
              cursor={{ fill: "var(--muted)", opacity: 0.1 }}
            />
            <Bar
              dataKey="count"
              radius={[0, 6, 6, 0]}
              className="hover:opacity-80 transition-opacity duration-200"
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Bar>
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}

// Lead Score Distribution Chart
interface LeadScoreChartProps {
  data: ChartDataPoint[];
}

export function LeadScoreChart({ data }: LeadScoreChartProps) {
  const chartConfig = {
    value: {
      label: "Number of Leads",
      color: "var(--chart-1)",
    },
  };

  return (
    <Card className="border-0 shadow-lg">
      <CardHeader className="pb-4">
        <CardTitle className="text-xl font-semibold">
          Lead Score Distribution
        </CardTitle>
        <CardDescription className="text-sm text-muted-foreground">
          Distribution of leads by score ranges
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <ChartContainer
          config={chartConfig}
          className="aspect-auto h-[300px] w-full"
        >
          <BarChart
            accessibilityLayer
            data={data}
            margin={{
              top: 20,
              right: 20,
              left: 20,
              bottom: 20,
            }}
          >
            <CartesianGrid
              strokeDasharray="3 3"
              vertical={false}
              className="stroke-muted/30"
            />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              className="text-xs fill-muted-foreground"
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => value.toLocaleString()}
              className="text-xs fill-muted-foreground"
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  labelFormatter={(label) => `Score Range: ${label}`}
                  formatter={(value) => [value.toLocaleString(), "Leads"]}
                />
              }
              cursor={{ fill: "var(--muted)", opacity: 0.1 }}
            />
            <Bar
              dataKey="value"
              fill="var(--color-value)"
              radius={[6, 6, 0, 0]}
              className="hover:opacity-80 transition-opacity duration-200"
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}

// Area Chart for Quality Trends
interface QualityTrendsChartProps {
  data: ChartDataPoint[];
}

export function QualityTrendsChart({ data }: QualityTrendsChartProps) {
  // Transform data for stacked area chart
  const transformedData = data.reduce((acc, item) => {
    const existing = acc.find((d) => d.date === item.date);
    if (existing) {
      existing[item.category!] = item.value;
    } else {
      acc.push({
        date: item.date,
        [item.category!]: item.value,
        hot: item.category === "hot" ? item.value : 0,
        warm: item.category === "warm" ? item.value : 0,
        cold: item.category === "cold" ? item.value : 0,
      });
    }
    return acc;
  }, [] as any[]);

  const chartConfig = {
    hot: {
      label: "Hot Leads",
      color: "var(--chart-2)",
    },
    warm: {
      label: "Warm Leads",
      color: "var(--chart-3)",
    },
    cold: {
      label: "Cold Leads",
      color: "var(--chart-4)",
    },
  };

  return (
    <Card className="border-0 shadow-lg">
      <CardHeader className="pb-4">
        <CardTitle className="text-xl font-semibold">
          Lead Quality Trends
        </CardTitle>
        <CardDescription className="text-sm text-muted-foreground">
          Lead quality distribution over the last 30 days
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <ChartContainer
          config={chartConfig}
          className="aspect-auto h-[350px] w-full"
        >
          <AreaChart
            accessibilityLayer
            data={transformedData}
            margin={{
              top: 20,
              right: 20,
              left: 20,
              bottom: 20,
            }}
          >
            <CartesianGrid
              strokeDasharray="3 3"
              vertical={false}
              className="stroke-muted/30"
            />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={(value) => {
                const date = new Date(value);
                return date.toLocaleDateString("en-US", {
                  month: "short",
                  day: "numeric",
                });
              }}
              className="text-xs fill-muted-foreground"
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => value.toLocaleString()}
              className="text-xs fill-muted-foreground"
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  labelFormatter={(value) => {
                    return new Date(value).toLocaleDateString("en-US", {
                      month: "long",
                      day: "numeric",
                      year: "numeric",
                    });
                  }}
                  formatter={(value, name) => [
                    value.toLocaleString(),
                    chartConfig[name as keyof typeof chartConfig]?.label ||
                      name,
                  ]}
                />
              }
              cursor={{ stroke: "var(--muted)", strokeWidth: 1 }}
            />
            <ChartLegend content={<ChartLegendContent />} />
            <Area
              type="monotone"
              dataKey="hot"
              stackId="1"
              stroke="var(--color-hot)"
              fill="var(--color-hot)"
              fillOpacity={0.8}
              strokeWidth={2}
            />
            <Area
              type="monotone"
              dataKey="warm"
              stackId="1"
              stroke="var(--color-warm)"
              fill="var(--color-warm)"
              fillOpacity={0.8}
              strokeWidth={2}
            />
            <Area
              type="monotone"
              dataKey="cold"
              stackId="1"
              stroke="var(--color-cold)"
              fill="var(--color-cold)"
              fillOpacity={0.8}
              strokeWidth={2}
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
