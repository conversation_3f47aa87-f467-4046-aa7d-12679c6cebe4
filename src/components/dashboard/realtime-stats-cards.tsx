"use client";

import { useRealtimeStats } from "@/hooks/use-realtime-stats";
import { DashboardStatsCards } from "./dashboard-stats";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { RefreshCw, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";

function StatsLoading() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
      {Array.from({ length: 6 }).map((_, i) => (
        <Card key={i}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-8 w-8 rounded-full" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-8 w-16 mb-2" />
            <Skeleton className="h-3 w-24" />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

function StatsError({ error, onRetry }: { error: any; onRetry: () => void }) {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center space-x-2 text-destructive mb-4">
          <AlertCircle className="h-5 w-5" />
          <h3 className="font-semibold">Failed to load statistics</h3>
        </div>
        <p className="text-sm text-muted-foreground mb-4">
          {error?.message ||
            "An error occurred while fetching dashboard statistics"}
        </p>
        <Button onClick={onRetry} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Try Again
        </Button>
      </CardContent>
    </Card>
  );
}

export function RealtimeStatsCards() {
  const { stats, isLoading, isValidating, error, refresh } = useRealtimeStats();

  if (error) {
    return <StatsError error={error} onRetry={refresh} />;
  }

  if (isLoading) {
    return <StatsLoading />;
  }

  return (
    <div className="relative">
      {/* Subtle loading indicator */}
      {isValidating && (
        <div className="absolute top-4 right-4 z-10">
          <div className="flex items-center space-x-2 bg-background/90 backdrop-blur-sm rounded-full px-3 py-1 shadow-sm border">
            <RefreshCw className="h-3 w-3 animate-spin text-blue-500" />
            <span className="text-xs text-muted-foreground">Updating...</span>
          </div>
        </div>
      )}

      {stats && <DashboardStatsCards stats={stats} />}
    </div>
  );
}
