"use client";

import { useRealtimeLeads } from '@/hooks/use-realtime-leads';
import { LeadsTable } from './leads-table';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { RefreshCw, AlertCircle, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';

function LeadsTableLoading() {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Table header skeleton */}
          <div className="flex items-center justify-between">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-8 w-24" />
          </div>
          
          {/* Table rows skeleton */}
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="flex items-center space-x-4 py-3 border-b">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-4 w-48" />
                <Skeleton className="h-3 w-32" />
              </div>
              <Skeleton className="h-6 w-16" />
              <Skeleton className="h-6 w-20" />
              <Skeleton className="h-8 w-8" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

function LeadsTableError({ error, onRetry }: { error: any; onRetry: () => void }) {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center space-x-2 text-destructive mb-4">
          <AlertCircle className="h-5 w-5" />
          <h3 className="font-semibold">Failed to load leads</h3>
        </div>
        <p className="text-sm text-muted-foreground mb-4">
          {error?.message || 'An error occurred while fetching leads data'}
        </p>
        <Button onClick={onRetry} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Try Again
        </Button>
      </CardContent>
    </Card>
  );
}

function EmptyLeadsState() {
  return (
    <Card>
      <CardContent className="p-12 text-center">
        <Users className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold mb-2">No leads yet</h3>
        <p className="text-muted-foreground">
          Leads will appear here as they are submitted through your forms.
        </p>
      </CardContent>
    </Card>
  );
}

export function RealtimeLeadsTable() {
  const { leads, total, pagination, isLoading, isValidating, error, refresh } = useRealtimeLeads({
    page: 1,
    limit: 20
  });

  if (error) {
    return <LeadsTableError error={error} onRetry={refresh} />;
  }

  if (isLoading) {
    return <LeadsTableLoading />;
  }

  if (!leads.length && !isLoading) {
    return <EmptyLeadsState />;
  }

  return (
    <div className="relative">
      {/* Real-time indicator */}
      {isValidating && (
        <div className="absolute top-4 right-4 z-10 bg-background/80 backdrop-blur-sm rounded-full p-2">
          <RefreshCw className="h-4 w-4 animate-spin text-muted-foreground" />
        </div>
      )}
      
      {/* Live indicator */}
      <div className="absolute top-4 left-4 z-10 bg-background/80 backdrop-blur-sm rounded-full px-3 py-1">
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
          <span className="text-xs font-medium text-muted-foreground">LIVE</span>
        </div>
      </div>

      <LeadsTable
        leads={leads}
        sources={[]} // We'll need to fetch sources separately or modify the component
        total={total}
        currentPage={pagination?.page || 1}
        pageSize={pagination?.limit || 20}
      />
    </div>
  );
}
