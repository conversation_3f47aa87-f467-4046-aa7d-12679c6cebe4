import { createClient } from "@/utils/supabase/server";

export interface ChartDataPoint {
  date: string;
  value: number;
  label?: string;
  category?: string;
}

export interface LeadTrendData {
  date: string;
  total: number;
  hot: number;
  warm: number;
  cold: number;
  converted: number;
}

export interface SourceDistribution {
  name: string;
  value: number;
  percentage: number;
  color?: string;
}

export interface ConversionFunnelData {
  stage: string;
  count: number;
  percentage: number;
  color: string;
}

export interface GeographicData {
  country: string;
  count: number;
  percentage: number;
}

export interface TimeSeriesData {
  timestamp: string;
  leads: number;
  activities: number;
}

// Get lead trends over time
export async function getLeadTrends(
  days: number = 30
): Promise<LeadTrendData[]> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from("lead_analytics_view")
    .select("*")
    .order("date", { ascending: true })
    .limit(days);

  if (error) throw error;

  return (
    data?.map((item) => ({
      date: item.date,
      total: item.total_leads || 0,
      hot: item.hot_leads || 0,
      warm: item.warm_leads || 0,
      cold: item.cold_leads || 0,
      converted: item.converted_leads || 0,
    })) || []
  );
}

// Get source distribution for pie charts
export async function getSourceDistribution(): Promise<SourceDistribution[]> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from("source_performance_view")
    .select("source_name, total_leads")
    .order("total_leads", { ascending: false });

  if (error) throw error;

  const total =
    data?.reduce((sum, item) => sum + (item.total_leads || 0), 0) || 0;

  const colors = [
    "#8884d8",
    "#82ca9d",
    "#ffc658",
    "#ff7300",
    "#00ff00",
    "#ff00ff",
    "#00ffff",
    "#ff0000",
  ];

  return (
    data?.map((item, index) => ({
      name: item.source_name,
      value: item.total_leads || 0,
      percentage:
        total > 0 ? Math.round(((item.total_leads || 0) / total) * 100) : 0,
      color: colors[index % colors.length],
    })) || []
  );
}

// Get conversion funnel data
export async function getConversionFunnel(): Promise<ConversionFunnelData[]> {
  const supabase = await createClient();

  // Get counts for each stage
  const { data: statusData, error } = await supabase
    .from("leads")
    .select("lead_status")
    .eq("is_deleted", false);

  if (error) throw error;

  const statusCounts = {
    new: 0,
    contacted: 0,
    qualified: 0,
    converted: 0,
    lost: 0,
  };

  statusData?.forEach((lead) => {
    if (lead.lead_status in statusCounts) {
      statusCounts[lead.lead_status as keyof typeof statusCounts]++;
    }
  });

  const total = Object.values(statusCounts).reduce(
    (sum, count) => sum + count,
    0
  );

  const stages = [
    { stage: "New Leads", count: statusCounts.new, color: "#8884d8" },
    { stage: "Contacted", count: statusCounts.contacted, color: "#82ca9d" },
    { stage: "Qualified", count: statusCounts.qualified, color: "#ffc658" },
    { stage: "Converted", count: statusCounts.converted, color: "#00ff00" },
    { stage: "Lost", count: statusCounts.lost, color: "#ff7300" },
  ];

  return stages.map((stage) => ({
    ...stage,
    percentage: total > 0 ? Math.round((stage.count / total) * 100) : 0,
  }));
}

// Get geographic distribution
export async function getGeographicDistribution(): Promise<GeographicData[]> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from("leads")
    .select("country")
    .eq("is_deleted", false)
    .not("country", "is", null);

  if (error) throw error;

  const countryCounts: Record<string, number> = {};
  data?.forEach((lead) => {
    if (lead.country) {
      countryCounts[lead.country] = (countryCounts[lead.country] || 0) + 1;
    }
  });

  const total = Object.values(countryCounts).reduce(
    (sum, count) => sum + count,
    0
  );

  return Object.entries(countryCounts)
    .map(([country, count]) => ({
      country,
      count,
      percentage: total > 0 ? Math.round((count / total) * 100) : 0,
    }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10); // Top 10 countries
}

// Get activity timeline data
export async function getActivityTimeline(
  days: number = 7
): Promise<TimeSeriesData[]> {
  const supabase = await createClient();

  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  // Get leads created per hour for the last week
  const { data: leadsData, error: leadsError } = await supabase
    .from("leads")
    .select("created_at")
    .eq("is_deleted", false)
    .gte("created_at", startDate.toISOString());

  if (leadsError) throw leadsError;

  // Get activities per hour for the last week
  const { data: activitiesData, error: activitiesError } = await supabase
    .from("lead_activities")
    .select("created_at")
    .gte("created_at", startDate.toISOString());

  if (activitiesError) throw activitiesError;

  // Group by hour
  const hourlyData: Record<string, { leads: number; activities: number }> = {};

  // Process leads
  leadsData?.forEach((lead) => {
    const hour =
      new Date(lead.created_at).toISOString().slice(0, 13) + ":00:00.000Z";
    if (!hourlyData[hour]) {
      hourlyData[hour] = { leads: 0, activities: 0 };
    }
    hourlyData[hour].leads++;
  });

  // Process activities
  activitiesData?.forEach((activity) => {
    const hour =
      new Date(activity.created_at).toISOString().slice(0, 13) + ":00:00.000Z";
    if (!hourlyData[hour]) {
      hourlyData[hour] = { leads: 0, activities: 0 };
    }
    hourlyData[hour].activities++;
  });

  return Object.entries(hourlyData)
    .map(([timestamp, data]) => ({
      timestamp,
      leads: data.leads,
      activities: data.activities,
    }))
    .sort((a, b) => a.timestamp.localeCompare(b.timestamp));
}

// Get lead quality distribution over time
export async function getQualityTrends(
  days: number = 30
): Promise<ChartDataPoint[]> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from("lead_analytics_view")
    .select("date, hot_leads, warm_leads, cold_leads")
    .order("date", { ascending: true })
    .limit(days);

  if (error) throw error;

  const result: ChartDataPoint[] = [];

  data?.forEach((item) => {
    result.push(
      {
        date: item.date,
        value: item.hot_leads || 0,
        category: "hot",
        label: "Hot Leads",
      },
      {
        date: item.date,
        value: item.warm_leads || 0,
        category: "warm",
        label: "Warm Leads",
      },
      {
        date: item.date,
        value: item.cold_leads || 0,
        category: "cold",
        label: "Cold Leads",
      }
    );
  });

  return result;
}

// Get campaign performance metrics
export async function getCampaignMetrics(days: number = 30): Promise<{
  totalCampaigns: number;
  totalLeads: number;
  totalRevenue: number;
  avgConversionRate: number;
  topCampaign: string;
}> {
  const supabase = await createClient();

  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  const { data, error } = await supabase
    .from("campaign_performance")
    .select("*")
    .gte("date", startDate.toISOString().split("T")[0]);

  if (error) throw error;

  if (!data || data.length === 0) {
    return {
      totalCampaigns: 0,
      totalLeads: 0,
      totalRevenue: 0,
      avgConversionRate: 0,
      topCampaign: "N/A",
    };
  }

  const totalCampaigns = new Set(data.map((item) => item.campaign_name)).size;
  const totalLeads = data.reduce(
    (sum, item) => sum + (item.leads_count || 0),
    0
  );
  const totalRevenue = data.reduce(
    (sum, item) => sum + parseFloat(item.total_revenue || "0"),
    0
  );
  const totalConverted = data.reduce(
    (sum, item) => sum + (item.converted_leads_count || 0),
    0
  );
  const avgConversionRate =
    totalLeads > 0 ? (totalConverted / totalLeads) * 100 : 0;

  // Find top campaign by leads
  const campaignLeads: Record<string, number> = {};
  data.forEach((item) => {
    campaignLeads[item.campaign_name] =
      (campaignLeads[item.campaign_name] || 0) + (item.leads_count || 0);
  });

  const topCampaign =
    Object.entries(campaignLeads).sort(([, a], [, b]) => b - a)[0]?.[0] ||
    "N/A";

  return {
    totalCampaigns,
    totalLeads,
    totalRevenue: Math.round(totalRevenue * 100) / 100,
    avgConversionRate: Math.round(avgConversionRate * 100) / 100,
    topCampaign,
  };
}

// Get lead score distribution
export async function getLeadScoreDistribution(): Promise<ChartDataPoint[]> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from("leads")
    .select("lead_score")
    .eq("is_deleted", false)
    .not("lead_score", "is", null);

  if (error) throw error;

  // Group scores into ranges
  const scoreRanges = {
    "0-20": 0,
    "21-40": 0,
    "41-60": 0,
    "61-80": 0,
    "81-100": 0,
  };

  data?.forEach((lead) => {
    const score = lead.lead_score || 0;
    if (score <= 20) scoreRanges["0-20"]++;
    else if (score <= 40) scoreRanges["21-40"]++;
    else if (score <= 60) scoreRanges["41-60"]++;
    else if (score <= 80) scoreRanges["61-80"]++;
    else scoreRanges["81-100"]++;
  });

  return Object.entries(scoreRanges).map(([range, count]) => ({
    date: range,
    value: count,
    label: `Score ${range}`,
  }));
}
