"use client";

import { useEffect } from 'react';
import useSWR, { mutate } from 'swr';
import { createClient } from '@/utils/supabase/client';
import type { Lead } from '@/lib/types';

interface LeadsResponse {
  success: boolean;
  data: {
    leads: Lead[];
    total: number;
  };
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

interface UseRealtimeLeadsOptions {
  page?: number;
  limit?: number;
  quality?: 'hot' | 'warm' | 'cold';
  status?: 'new' | 'contacted' | 'qualified' | 'converted' | 'lost';
  sourceId?: string;
  search?: string;
}

export function useRealtimeLeads(options: UseRealtimeLeadsOptions = {}) {
  const {
    page = 1,
    limit = 20,
    quality,
    status,
    sourceId,
    search
  } = options;

  // Build query string
  const queryParams = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
  });
  
  if (quality) queryParams.set('quality', quality);
  if (status) queryParams.set('status', status);
  if (sourceId) queryParams.set('sourceId', sourceId);
  if (search) queryParams.set('search', search);

  const key = `/api/leads?${queryParams.toString()}`;

  const { data, error, isLoading, isValidating } = useSWR<LeadsResponse>(
    key,
    {
      revalidateOnFocus: false,
      revalidateOnMount: true,
      // Keep previous data while loading new data
      keepPreviousData: true,
    }
  );

  // Set up real-time subscription
  useEffect(() => {
    const supabase = createClient();
    
    const channel = supabase
      .channel('leads-realtime')
      .on(
        'postgres_changes',
        { event: 'INSERT', schema: 'public', table: 'leads' },
        (payload) => {
          console.log('New lead inserted:', payload.new);
          
          // Only update if we're on the first page to avoid confusion
          if (page === 1) {
            // Optimistic update: add new lead to the beginning of the list
            mutate(
              key,
              (currentData: LeadsResponse | undefined) => {
                if (!currentData?.data) return currentData;
                
                const newLead = payload.new as Lead;
                const updatedLeads = [newLead, ...currentData.data.leads.slice(0, limit - 1)];
                
                return {
                  ...currentData,
                  data: {
                    leads: updatedLeads,
                    total: currentData.data.total + 1
                  },
                  pagination: {
                    ...currentData.pagination,
                    total: currentData.pagination.total + 1,
                    totalPages: Math.ceil((currentData.pagination.total + 1) / limit)
                  }
                };
              },
              {
                revalidate: false, // Don't revalidate immediately
                populateCache: true,
              }
            );
          }
          
          // Revalidate after a short delay to ensure consistency
          setTimeout(() => {
            mutate(key);
          }, 2000);
        }
      )
      .on(
        'postgres_changes',
        { event: 'UPDATE', schema: 'public', table: 'leads' },
        (payload) => {
          console.log('Lead updated:', payload.new);
          
          // For updates, optimistically update the specific lead
          mutate(
            key,
            (currentData: LeadsResponse | undefined) => {
              if (!currentData?.data) return currentData;
              
              const updatedLead = payload.new as Lead;
              const updatedLeads = currentData.data.leads.map(lead => 
                lead.id === updatedLead.id ? updatedLead : lead
              );
              
              return {
                ...currentData,
                data: {
                  ...currentData.data,
                  leads: updatedLeads
                }
              };
            },
            {
              revalidate: false,
              populateCache: true,
            }
          );
        }
      )
      .on(
        'postgres_changes',
        { event: 'DELETE', schema: 'public', table: 'leads' },
        (payload) => {
          console.log('Lead deleted:', payload.old);
          
          // For deletes, remove the lead from the list
          mutate(
            key,
            (currentData: LeadsResponse | undefined) => {
              if (!currentData?.data) return currentData;
              
              const deletedLeadId = (payload.old as Lead).id;
              const updatedLeads = currentData.data.leads.filter(lead => lead.id !== deletedLeadId);
              
              return {
                ...currentData,
                data: {
                  leads: updatedLeads,
                  total: Math.max(0, currentData.data.total - 1)
                },
                pagination: {
                  ...currentData.pagination,
                  total: Math.max(0, currentData.pagination.total - 1),
                  totalPages: Math.ceil(Math.max(0, currentData.pagination.total - 1) / limit)
                }
              };
            },
            {
              revalidate: false,
              populateCache: true,
            }
          );
        }
      )
      .subscribe();

    return () => supabase.removeChannel(channel);
  }, [key, page, limit]);

  return {
    leads: data?.data?.leads || [],
    total: data?.data?.total || 0,
    pagination: data?.pagination,
    isLoading,
    isValidating,
    error,
    // Utility function to manually refresh
    refresh: () => mutate(key),
  };
}
