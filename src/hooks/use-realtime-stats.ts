"use client";

import { useEffect } from 'react';
import useSWR, { mutate } from 'swr';
import { createClient } from '@/utils/supabase/client';
import type { DashboardStats } from '@/lib/types';

interface StatsResponse {
  success: boolean;
  data: DashboardStats;
}

export function useRealtimeStats() {
  const { data, error, isLoading, isValidating } = useSWR<StatsResponse>(
    '/api/dashboard/stats',
    {
      revalidateOnFocus: false,
      refreshInterval: 30000, // Fallback refresh every 30 seconds
      // Keep previous data while loading new data
      keepPreviousData: true,
    }
  );

  // Set up real-time subscription
  useEffect(() => {
    const supabase = createClient();
    
    const channel = supabase
      .channel('stats-realtime')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'leads' },
        (payload) => {
          console.log('Lead change detected for stats update:', payload.eventType);
          
          // For stats, we'll revalidate after any lead change
          // We add a small delay to allow the database to process the change
          setTimeout(() => {
            mutate('/api/dashboard/stats');
          }, 1000);
        }
      )
      .subscribe();

    return () => supabase.removeChannel(channel);
  }, []);

  return {
    stats: data?.data,
    isLoading,
    isValidating,
    error,
    // Utility function to manually refresh
    refresh: () => mutate('/api/dashboard/stats'),
  };
}
