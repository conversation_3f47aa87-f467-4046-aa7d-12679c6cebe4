import { redirect } from "next/navigation";
import { createClient } from "@/utils/supabase/server";
import { Suspense } from "react";

// Dashboard stats now handled by real-time components
import {
  LeadTrendsChart,
  SourceDistribution<PERSON>hart,
  ConversionFunnel<PERSON>hart,
  LeadScoreChart,
  QualityTrendsChart,
} from "@/components/dashboard/charts";
import {
  RecentActivities,
  ActivitySummary,
} from "@/components/dashboard/recent-activities";
// LeadsTable replaced by RealtimeLeadsTable
import { RealtimeStatsCards } from "@/components/dashboard/realtime-stats-cards";
import { RealtimeLeadsTable } from "@/components/dashboard/realtime-leads-table";
import { NotificationSettings } from "@/components/realtime/notification-settings";
import { ConnectionStatus } from "@/components/realtime/connection-status";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { logout } from "@/app/(auth)/login/actions";
import { BarChart3, TrendingUp, Users, Activity, Zap } from "lucide-react";
import {
  getConversionFunnel,
  getLeadScoreDistribution,
  getLeadTrends,
  getQualityTrends,
  getRecentActivities,
  getSourceDistribution,
} from "@/lib/database";

// Loading components for charts (keeping only what's used)

function ChartLoading() {
  return (
    <Card>
      <CardHeader>
        <Skeleton className="h-6 w-32" />
        <Skeleton className="h-4 w-48" />
      </CardHeader>
      <CardContent>
        <Skeleton className="h-64 w-full" />
      </CardContent>
    </Card>
  );
}

// Data fetching components (keeping only what's used)

async function DashboardCharts() {
  try {
    const [
      leadTrends,
      sourceDistribution,
      conversionFunnel,
      leadScoreDistribution,
      qualityTrends,
    ] = await Promise.all([
      getLeadTrends(30),
      getSourceDistribution(),
      getConversionFunnel(),
      getLeadScoreDistribution(),
      getQualityTrends(30),
    ]);

    return (
      <div className="grid gap-6">
        <div className="grid gap-6 lg:grid-cols-2">
          <LeadTrendsChart data={leadTrends} />
          <SourceDistributionChart data={sourceDistribution} />
        </div>
        <div className="grid gap-6 lg:grid-cols-2">
          <ConversionFunnelChart data={conversionFunnel} />
          <LeadScoreChart data={leadScoreDistribution} />
        </div>
        <QualityTrendsChart data={qualityTrends} />
      </div>
    );
  } catch (error) {
    console.error("Error fetching chart data:", error);
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-muted-foreground">Unable to load charts</p>
        </CardContent>
      </Card>
    );
  }
}

async function DashboardActivities() {
  try {
    const activities = await getRecentActivities(10);
    return (
      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <RecentActivities activities={activities} />
        </div>
        <ActivitySummary activities={activities} />
      </div>
    );
  } catch (error) {
    console.error("Error fetching activities:", error);
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-muted-foreground">Unable to load activities</p>
        </CardContent>
      </Card>
    );
  }
}

// DashboardLeadsTable replaced by RealtimeLeadsTable

export default async function PrivatePage() {
  const supabase = await createClient();
  const { data, error } = await supabase.auth.getUser();

  if (error || !data?.user) {
    redirect("/login");
  }

  return (
    <div className="container mx-auto py-8 px-4 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Zap className="h-8 w-8 text-yellow-500" />
            Lead Analytics Dashboard
          </h1>
          <p className="text-muted-foreground">
            Track and analyze your lead generation performance in real-time
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <NotificationSettings />
          <Button variant="outline" size="sm">
            <BarChart3 className="h-4 w-4 mr-2" />
            Export Report
          </Button>
          <form action={logout} className="inline">
            <Button type="submit" variant="outline" size="sm">
              Sign Out
            </Button>
          </form>
        </div>
      </div>

      {/* Real-time Dashboard Stats */}
      <RealtimeStatsCards />

      {/* Charts Section */}
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          <h2 className="text-2xl font-semibold">Analytics & Trends</h2>
        </div>
        <Suspense
          fallback={
            <div className="grid gap-6">
              <ChartLoading />
              <ChartLoading />
            </div>
          }
        >
          <DashboardCharts />
        </Suspense>
      </div>

      {/* Activities Section */}
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          <h2 className="text-2xl font-semibold">Recent Activities</h2>
        </div>
        <Suspense fallback={<ChartLoading />}>
          <DashboardActivities />
        </Suspense>
      </div>

      {/* Real-time Leads Table Section */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            <h2 className="text-2xl font-semibold">Recent Leads</h2>
          </div>
          <ConnectionStatus />
        </div>
        <RealtimeLeadsTable />
      </div>
    </div>
  );
}
