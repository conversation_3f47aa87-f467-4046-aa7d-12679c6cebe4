import { redirect } from "next/navigation";
import { createClient } from "@/utils/supabase/server";
import { SWRProvider } from "@/app/providers";
import { RealtimeNotifications } from "@/components/realtime/realtime-notifications";

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    redirect("/login");
  }

  return (
    <SWRProvider>
      <RealtimeNotifications />
      {children}
    </SWRProvider>
  );
}
