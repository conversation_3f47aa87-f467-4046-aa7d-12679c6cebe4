import { NextRequest, NextResponse } from "next/server";
import { Lead } from "@/lib/types";
import { createClient } from "@/utils/supabase/server";

async function getLeads(
  options: {
    page?: number;
    limit?: number;
    quality?: "hot" | "warm" | "cold";
    status?: "new" | "contacted" | "qualified" | "converted" | "lost";
    sourceId?: string;
    search?: string;
  } = {}
): Promise<{ leads: Lead[]; total: number }> {
  const supabase = await createClient();
  const { page = 1, limit = 50, quality, status, sourceId, search } = options;

  let query = supabase
    .from("leads")
    .select("*", { count: "exact" })
    .eq("is_deleted", false);

  // Apply filters
  if (quality) query = query.eq("lead_quality", quality);
  if (status) query = query.eq("lead_status", status);
  if (sourceId) query = query.eq("lead_source_id", sourceId);
  if (search) {
    query = query.or(
      `email.ilike.%${search}%,first_name.ilike.%${search}%,last_name.ilike.%${search}%,company.ilike.%${search}%`
    );
  }

  // Apply pagination
  const from = (page - 1) * limit;
  const to = from + limit - 1;

  const { data, error, count } = await query
    .order("created_at", { ascending: false })
    .range(from, to);

  if (error) throw error;

  const leads =
    data?.map((item) => ({
      id: item.id,
      email: item.email,
      firstName: item.first_name || undefined,
      lastName: item.last_name || undefined,
      phone: item.phone || undefined,
      company: item.company || undefined,
      jobTitle: item.job_title || undefined,
      website: item.website || undefined,
      leadSourceId: item.lead_source_id || undefined,
      utmSource: item.utm_source || undefined,
      utmMedium: item.utm_medium || undefined,
      utmCampaign: item.utm_campaign || undefined,
      utmContent: item.utm_content || undefined,
      utmTerm: item.utm_term || undefined,
      ipAddress: item.ip_address || undefined,
      country: item.country || undefined,
      state: item.state || undefined,
      city: item.city || undefined,
      timezone: item.timezone || undefined,
      leadScore: item.lead_score || 0,
      leadQuality: item.lead_quality as "hot" | "warm" | "cold",
      leadStatus: item.lead_status as
        | "new"
        | "contacted"
        | "qualified"
        | "converted"
        | "lost",
      isVerified: item.is_verified || false,
      isDuplicate: item.is_duplicate || false,
      duplicateOf: item.duplicate_of || undefined,
      isDeleted: item.is_deleted || false,
      createdAt: item.created_at,
      updatedAt: item.updated_at,
      lastContactedAt: item.last_contacted_at || undefined,
      convertedAt: item.converted_at || undefined,
    })) || [];

  return { leads, total: count || 0 };
}

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const quality = searchParams.get("quality") as
      | "hot"
      | "warm"
      | "cold"
      | undefined;
    const status = searchParams.get("status") as
      | "new"
      | "contacted"
      | "qualified"
      | "converted"
      | "lost"
      | undefined;
    const sourceId = searchParams.get("sourceId") || undefined;
    const search = searchParams.get("search") || undefined;

    const result = await getLeads({
      page,
      limit,
      quality,
      status,
      sourceId,
      search,
    });

    return NextResponse.json({
      success: true,
      data: result,
      pagination: {
        page,
        limit,
        total: result.total,
        totalPages: Math.ceil(result.total / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching leads:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch leads" },
      { status: 500 }
    );
  }
}
