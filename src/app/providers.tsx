"use client";

import { SWRConfig } from 'swr';

// Global fetcher function following SWR best practices
const fetcher = async (...args: [string, ...any[]]) => {
  const res = await fetch(...args);
  
  if (!res.ok) {
    const error = new Error('An error occurred while fetching the data.');
    // Attach extra info to the error object
    (error as any).info = await res.json();
    (error as any).status = res.status;
    throw error;
  }
  
  return res.json();
};

export function SWRProvider({ children }: { children: React.ReactNode }) {
  return (
    <SWRConfig 
      value={{
        fetcher,
        // Disable automatic revalidation on focus since we have real-time updates
        revalidateOnFocus: false,
        // Keep revalidation on reconnect for reliability
        revalidateOnReconnect: true,
        // Disable interval refresh - we'll use real-time updates instead
        refreshInterval: 0,
        // Error retry configuration
        errorRetryCount: 3,
        errorRetryInterval: 5000,
        // Dedupe requests within 2 seconds
        dedupingInterval: 2000,
        // Keep data fresh for 5 minutes
        focusThrottleInterval: 5 * 60 * 1000,
        // Show loading state immediately
        loadingTimeout: 3000,
        // Global error handler
        onError: (error, key) => {
          console.error('SWR Error:', { error, key });
          // You can add global error reporting here
        },
        // Global success handler
        onSuccess: (data, key, config) => {
          // You can add global success tracking here
          console.log('SWR Success:', { key, dataLength: Array.isArray(data) ? data.length : 'object' });
        },
      }}
    >
      {children}
    </SWRConfig>
  );
}
